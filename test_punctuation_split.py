#!/usr/bin/env python3
"""
测试新的标点符号分割功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.file_utils import split_by_punctuation_near_target, split_text_into_chapters

def test_punctuation_split():
    """测试标点符号分割功能"""

    # 创建一个超过2000字的长文本用于测试
    base_story = """这是一个很长的故事开始。主人公小明是一个普通的学生，他每天都要上学。今天是星期一，小明起床后发现外面下雨了。他匆忙穿好衣服，拿起书包就往学校跑。路上他遇到了同学小红，两人一起走向学校。到了学校后，小明发现自己忘记带作业了！他非常着急，不知道该怎么办。老师走进教室，开始检查作业。小明的心跳得很快，手心都出汗了。"小明，你的作业呢？"老师问道。小明红着脸站起来，小声说："老师，我忘记带了。"老师看了看他，说："下次要记得带作业，这次就算了。"小明松了一口气。下课后，小明和小红一起去图书馆。他们找了一个安静的角落坐下来，开始做今天的作业。小明很认真地写着，生怕再次忘记。时间过得很快，转眼就到了放学时间。小明收拾好书包，和小红告别后就回家了。在回家的路上，他想起了今天发生的事情，决定以后要更加细心。回到家后，小明把今天的作业都做完了。他还检查了明天要带的东西，确保不会再忘记任何重要的物品。妈妈看到他这么认真，很高兴地夸奖了他。晚上，小明躺在床上回想今天的经历。虽然早上忘记带作业让他很紧张，但是老师的宽容和自己的反思让他学到了很多。他决定明天要做得更好。第二天，小明早早就起床了。他仔细检查了书包，确认所有的作业和用品都带齐了。走在上学的路上，他感到很自信。到了学校，小明主动把作业交给了老师。老师看到他的改变，露出了满意的笑容。这让小明更加坚定了要做一个负责任学生的决心。从那以后，小明再也没有忘记过作业。他不仅学习成绩提高了，还成为了班上的好榜样。同学们都很佩服他的改变。这个故事告诉我们，每个人都会犯错误，但重要的是要从错误中学习，不断改进自己。只要有决心和毅力，就一定能够成为更好的人。"""

    # 重复故事内容以创建超过2000字的文本
    test_text = ""
    for i in range(5):  # 重复5次，确保超过2000字
        test_text += f"\n\n故事第{i+1}部分：\n" + base_story

    print("原始文本长度:", len(test_text))
    print("=" * 50)
    
    # 测试标点符号分割
    chunks = split_by_punctuation_near_target(test_text, target_length=500, tolerance=100)
    
    print(f"分割成 {len(chunks)} 个块:")
    for i, chunk in enumerate(chunks, 1):
        print(f"\n第{i}块 (长度: {len(chunk)}):")
        print("-" * 30)
        print(chunk[:100] + "..." if len(chunk) > 100 else chunk)
    
    print("\n" + "=" * 50)
    
    # 测试完整的章节分割功能
    chapters = split_text_into_chapters(test_text)
    
    print(f"分割成 {len(chapters)} 个章节:")
    for chapter in chapters:
        print(f"\n{chapter['title']} (长度: {len(chapter['content'])}):")
        print("-" * 30)
        content_preview = chapter['content'][:100] + "..." if len(chapter['content']) > 100 else chapter['content']
        print(content_preview)

def test_with_chapters():
    """测试有章节标题的文本"""

    test_text_with_chapters = """第一章 开始

这是第一章的内容。主人公开始了他的冒险之旅。这里有更多的内容来测试章节分割功能。

第二章 冒险

在第二章中，主人公遇到了很多困难和挑战。他需要克服各种障碍才能继续前进。

第三章 成长

通过前面的经历，主人公得到了成长和进步。他变得更加勇敢和智慧。"""

    print("\n" + "=" * 50)
    print("测试有章节标题的文本:")
    print("=" * 50)
    print("原始文本:")
    print(repr(test_text_with_chapters))
    print("\n")

    # 调试正则表达式匹配
    import re
    chapter_patterns = [
        r'^第[一二三四五六七八九十百千万\d]+章[^\n]*',  # 行首的第X章 + 可能的章节标题
        r'^第[0-9]+章[^\n]*',  # 行首的第数字章 + 可能的章节标题
        r'^Chapter\s*\d+[^\n]*',  # 行首的Chapter数字 + 可能的标题
        r'^[第]?[0-9]+[章节][^\n]*',  # 行首的数字章/节 + 可能的标题
        r'^[第]?[一二三四五六七八九十百千万]+[章节][^\n]*',  # 行首的中文数字章/节 + 可能的标题
    ]

    for i, pattern in enumerate(chapter_patterns):
        matches = re.findall(pattern, test_text_with_chapters, flags=re.IGNORECASE | re.MULTILINE)
        print(f"模式 {i+1}: {pattern}")
        print(f"匹配结果: {matches}")
        if len(matches) > 1:
            parts = re.split(f'({pattern})', test_text_with_chapters, flags=re.IGNORECASE | re.MULTILINE)
            print(f"分割结果: {[repr(p) for p in parts if p.strip()]}")
            break
        print()

    chapters = split_text_into_chapters(test_text_with_chapters)

    print(f"\n识别出 {len(chapters)} 个章节:")
    for chapter in chapters:
        print(f"\n{chapter['title']}:")
        print("-" * 20)
        print(repr(chapter['content']))

if __name__ == "__main__":
    test_punctuation_split()
    test_with_chapters()
